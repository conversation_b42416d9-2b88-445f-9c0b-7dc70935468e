/** biome-ignore-all lint/style/useTemplate: <explanation> */
import { createWalletClient, createPublicClient, http, parseEther, formatEther } from 'viem'
import { base } from 'viem/chains'
import { privateKeyToAccount } from 'viem/accounts'
import { readFileSync } from 'fs'

// ERC20 Token ABI (standard functions we need)
const ERC20_ABI = [
  {
    inputs: [
      { name: 'to', type: 'address' },
      { name: 'amount', type: 'uint256' }
    ],
    name: 'transfer',
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function'
  },
  {
    inputs: [{ name: 'account', type: 'address' }],
    name: 'balanceOf',
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function'
  },
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function'
  },
  {
    inputs: [],
    name: 'totalSupply',
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function'
  }
] as const

interface UserAllocation {
  address: string
  allocation: number
  totalUsageMax: bigint
  tokenAmount: bigint
}

interface DistributionConfig {
  erc20TokenAddress: string
  distributionPercentage: number // Percentage of totalUsageMax to distribute (e.g., 10 for 10%)
  privateKey: string
  csvFilePath: string
  dryRun?: boolean // Set to true to simulate without actual transactions
}

class ERC20TokenDistributor {
  private publicClient: any
  private walletClient: any
  private account: any
  private config: DistributionConfig

  constructor(config: DistributionConfig) {
    this.config = config

    // Create public client for reading blockchain data
    this.publicClient = createPublicClient({
      chain: base,
      transport: http("https://base-mainnet.g.alchemy.com/v2/********************************"),
      batch: {
        multicall: true,
      },
    })

    // Create account from private key
    this.account = privateKeyToAccount(config.privateKey as `0x${string}`)

    // Create wallet client for sending transactions
    this.walletClient = createWalletClient({
      account: this.account,
      chain: base,
      transport: http("https://base-mainnet.g.alchemy.com/v2/********************************"),
    })
  }

  /**
   * Read and parse the CSV file containing user data
   */
  private async readUserData(): Promise<UserAllocation[]> {
    console.log('📖 Reading CSV data...')

    const csvContent = readFileSync(this.config.csvFilePath, 'utf-8')
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')

    // Validate CSV headers
    const expectedHeaders = ['address', 'allocation', 'totalUsageMax']
    if (!expectedHeaders.every(header => headers.includes(header))) {
      throw new Error(`CSV must contain headers: ${expectedHeaders.join(', ')}`)
    }

    const userData: UserAllocation[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',')
      const address = values[0].trim()
      const allocation = parseFloat(values[1])
      const totalUsageMax = BigInt(values[2])
      const percentage = parseFloat(values[3])

      // Skip users with zero usage
      if (totalUsageMax === 0n) {
        continue
      }

      // Calculate token amount based on percentage of totalUsageMax
      // const tge = (totalUsageMax * BigInt(25)) / 100n
      const tokenAmount = (totalUsageMax * BigInt(percentage)) / 100n
      userData.push({
        address,
        allocation,
        totalUsageMax,
        tokenAmount: tokenAmount,
        // tokenAmount: tokenAmount - tge,
      })
    }

    console.log(`✅ Found ${userData.length} users with non-zero usage`)
    return userData
  }

  /**
   * Get ERC20 token information
   */
  private async getTokenInfo() {
    console.log('🔍 Getting token information...')

    const [decimals, totalSupply, balance] = await Promise.all([
      this.publicClient.readContract({
        address: this.config.erc20TokenAddress,
        abi: ERC20_ABI,
        functionName: 'decimals',
      }),
      this.publicClient.readContract({
        address: this.config.erc20TokenAddress,
        abi: ERC20_ABI,
        functionName: 'totalSupply',
      }),
      this.publicClient.readContract({
        address: this.config.erc20TokenAddress,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: [this.account.address],
      })
    ])

    console.log(`📊 Token decimals: ${decimals}`)
    console.log(`📊 Total supply: ${formatEther(totalSupply as bigint)}`)
    console.log(`💰 Distributor balance: ${formatEther(balance as bigint)}`)

    return { decimals: decimals as number, totalSupply: totalSupply as bigint, balance: balance as bigint }
  }

  /**
   * Calculate distribution summary
   */
  private calculateDistributionSummary(userData: UserAllocation[]) {
    const totalTokensToDistribute = userData.reduce((sum, user) => sum + user.tokenAmount, 0n)
    const totalUsageSum = userData.reduce((sum, user) => sum + user.totalUsageMax, 0n)

    console.log('\n📈 DISTRIBUTION SUMMARY')
    console.log('='.repeat(50))
    console.log(`👥 Recipients: ${userData.length}`)
    console.log(`📊 Distribution percentage: ${this.config.distributionPercentage}%`)
    console.log(`🎯 Total usage sum: ${formatEther(totalUsageSum)}`)
    console.log(`💸 Total tokens to distribute: ${formatEther(totalTokensToDistribute)}`)
    console.log(`💰 Average per user: ${formatEther(totalTokensToDistribute / BigInt(userData.length))}`)

    return { totalTokensToDistribute, totalUsageSum }
  }

  /**
   * Distribute tokens to users
   */
  async distributeTokens(): Promise<void> {
    try {
      // Read user data
      const userData = await this.readUserData()

      if (userData.length === 0) {
        console.log('❌ No users found with non-zero usage. Exiting.')
        return
      }

      // Get token information
      const tokenInfo = await this.getTokenInfo()

      // Calculate distribution summary
      const { totalTokensToDistribute } = this.calculateDistributionSummary(userData)

      // Check if distributor has enough balance
      if (tokenInfo.balance < totalTokensToDistribute) {
        throw new Error(
          `Insufficient balance. Need: ${formatEther(totalTokensToDistribute)}, Have: ${formatEther(tokenInfo.balance)}`
        )
      }

      if (this.config.dryRun) {
        console.log('\n🧪 DRY RUN MODE - No actual transactions will be sent')
        this.printDistributionPreview(userData.slice(0, 10)) // Show first 10 users
        return
      }

      // Confirm distribution
      console.log('\n⚠️  Ready to distribute tokens. This will send real transactions!')
      console.log('Press Ctrl+C to cancel, or any key to continue...')

      // Start distribution
      console.log('\n🚀 Starting token distribution...')

      let successCount = 0
      let failureCount = 0
      const batchSize = 1 // Process in batches to avoid rate limiting

      for (let i = 0; i < userData.length; i += batchSize) {
        const batch = userData.slice(i, i + batchSize)
        console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(userData.length / batchSize)}`)

        for (const user of batch) {
          try {
            console.log(`💸 Sending ${formatEther(user.tokenAmount)} tokens to ${user.address}...`)

            const hash = await this.walletClient.writeContract({
              address: this.config.erc20TokenAddress,
              abi: ERC20_ABI,
              functionName: 'transfer',
              args: [user.address as `0x${string}`, user.tokenAmount],
            })

            console.log(`✅ Transaction sent: ${hash}`)
            successCount++

            // Small delay between transactions
            await new Promise(resolve => setTimeout(resolve, 3000))

          } catch (error) {
            console.error(`❌ Failed to send to ${user.address}:`, error)
            failureCount++
          }
        }

        // Delay between batches
        if (i + batchSize < userData.length) {
          console.log('⏳ Waiting before next batch...')
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }

      console.log('\n🎉 DISTRIBUTION COMPLETE!')
      console.log('='.repeat(50))
      console.log(`✅ Successful transfers: ${successCount}`)
      console.log(`❌ Failed transfers: ${failureCount}`)
      console.log(`💸 Total tokens distributed: ${formatEther(totalTokensToDistribute)}`)

    } catch (error) {
      console.error('💥 Distribution failed:', error)
      throw error
    }
  }

  /**
   * Print distribution preview for dry run
   */
  private printDistributionPreview(userData: UserAllocation[]) {
    console.log('\n👀 DISTRIBUTION PREVIEW (First 10 users)')
    console.log('='.repeat(80))
    console.log('Address'.padEnd(42) + ' | ' + 'Usage Max'.padEnd(20) + ' | ' + 'Token Amount')
    console.log('-'.repeat(80))

    userData.forEach(user => {
      console.log(
        user.address.padEnd(42) + ' | ' +
        formatEther(user.totalUsageMax).padEnd(20) + ' | ' +
        formatEther(user.tokenAmount)
      )
    })

    if (userData.length < 10) {
      console.log(`\n... and ${userData.length - 10} more users`)
    }
  }

  /**
   * Get distribution statistics without executing
   */
  async getDistributionStats(): Promise<{
    totalRecipients: number
    totalTokensToDistribute: bigint
    averagePerUser: bigint
    distributorBalance: bigint
    canDistribute: boolean
  }> {
    const userData = await this.readUserData()
    const tokenInfo = await this.getTokenInfo()
    const totalTokensToDistribute = userData.reduce((sum, user) => sum + user.tokenAmount, 0n)

    return {
      totalRecipients: userData.length,
      totalTokensToDistribute,
      averagePerUser: userData.length > 0 ? totalTokensToDistribute / BigInt(userData.length) : 0n,
      distributorBalance: tokenInfo.balance,
      canDistribute: tokenInfo.balance >= totalTokensToDistribute
    }
  }
}

export { ERC20TokenDistributor, type DistributionConfig }